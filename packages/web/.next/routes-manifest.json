{"version": 3, "caseSensitive": false, "basePath": "", "rewrites": {"beforeFiles": [], "afterFiles": [{"source": "/sitemap.xml", "destination": "/api/sitemap.xml", "regex": "^\\/sitemap\\.xml(?:\\/)?$", "check": true}, {"source": "/robots.txt", "destination": "/api/robots.txt", "regex": "^\\/robots\\.txt(?:\\/)?$", "check": true}], "fallback": []}, "redirects": [{"source": "/:path+/", "destination": "/:path+", "permanent": true, "internal": true, "regex": "^(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))\\/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Cache-Control", "value": "no-store, must-revalidate"}], "regex": "^\\/api(?:\\/((?:[^\\/]+?)(?:\\/(?:[^\\/]+?))*))?(?:\\/)?$"}]}